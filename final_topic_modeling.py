#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整版中文文本主题建模分析
处理所有数据，包含：数据预处理、LDA主题建模、主题分类、可视化
"""

import pandas as pd
import numpy as np
import jieba
import re
import os
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.decomposition import LatentDirichletAllocation
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def preprocess_text(text, stopwords):
    """文本预处理函数"""
    if pd.isna(text):
        return ""
    
    # 转换为字符串
    text = str(text)
    
    # 去除非中文字符
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', ' ', text)
    
    # jieba分词
    words = jieba.cut(text)
    
    # 过滤停用词和短词
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= 2 and 
            word not in stopwords and 
            not word.isdigit() and
            re.match(r'^[\u4e00-\u9fa5]+$', word)):
            filtered_words.append(word)
    
    return ' '.join(filtered_words)

def main():
    print("开始完整版主题建模分析...")
    
    # 1. 加载数据
    print("正在加载数据...")
    try:
        df = pd.read_csv("1_带标签.csv", encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv("1_带标签.csv", encoding='gbk')
    
    print(f"数据加载完成，共{len(df)}条记录")
    
    # 检查列名
    if '全文内容' not in df.columns:
        print("错误: 数据中未找到'全文内容'列")
        return
    
    # 去除空值
    df = df.dropna(subset=['全文内容'])
    print(f"去除空值后，剩余{len(df)}条记录")
    
    # 2. 加载停用词
    print("正在加载停用词...")
    with open("stopword_cn.txt", 'r', encoding='utf-8') as f:
        stopwords = set([line.strip() for line in f.readlines()])
    print(f"停用词加载完成，共{len(stopwords)}个")
    
    # 3. 文本预处理
    print("正在进行文本预处理...")
    processed_texts = []
    
    for i, text in enumerate(df['全文内容']):
        if i % 5000 == 0:
            print(f"处理进度: {i}/{len(df)}")
        
        processed = preprocess_text(text, stopwords)
        processed_texts.append(processed)
    
    # 过滤空文本
    valid_indices = [i for i, text in enumerate(processed_texts) if text.strip()]
    processed_texts = [processed_texts[i] for i in valid_indices]
    df = df.iloc[valid_indices].reset_index(drop=True)
    
    print(f"文本预处理完成，有效文本{len(processed_texts)}条")
    
    # 4. 训练LDA模型
    print("正在训练LDA模型...")
    
    vectorizer = CountVectorizer(
        max_features=1000,
        min_df=5,
        max_df=0.7,
        token_pattern=r'\b\w+\b'
    )
    
    doc_term_matrix = vectorizer.fit_transform(processed_texts)
    print(f"词汇表大小: {doc_term_matrix.shape[1]}")
    
    lda_model = LatentDirichletAllocation(
        n_components=6,
        random_state=42,
        max_iter=50,
        learning_method='batch',
        n_jobs=-1  # 使用多核处理
    )
    
    doc_topic_matrix = lda_model.fit_transform(doc_term_matrix)
    
    print("LDA模型训练完成")
    
    # 5. 获取主题关键词
    print("正在获取主题关键词...")
    feature_names = vectorizer.get_feature_names_out()
    topics_words = []
    
    for topic_idx, topic in enumerate(lda_model.components_):
        top_words_idx = topic.argsort()[-10:][::-1]
        top_words = [feature_names[i] for i in top_words_idx]
        top_weights = [topic[i] for i in top_words_idx]
        
        topics_words.append({
            'topic_id': topic_idx + 1,
            'words': top_words,
            'weights': top_weights
        })
    
    # 6. 为文档分配主题
    print("正在为文档分配主题...")
    doc_topics = []
    for doc_topic_dist in doc_topic_matrix:
        main_topic = np.argmax(doc_topic_dist) + 1
        doc_topics.append(main_topic)
    
    df['主题'] = doc_topics
    
    # 7. 创建输出目录
    output_dir = "主题建模"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 8. 保存结果
    print("正在保存结果...")
    
    # 保存主题关键词
    topic_data = []
    for topic in topics_words:
        for i, (word, weight) in enumerate(zip(topic['words'], topic['weights']), 1):
            topic_data.append({
                '主题编号': topic['topic_id'],
                '排名': i,
                '关键词': word,
                '权重': round(weight, 4)
            })
    
    topic_df = pd.DataFrame(topic_data)
    topic_df.to_csv(os.path.join(output_dir, '主题关键词表_完整版.csv'), 
                    index=False, encoding='utf-8-sig')
    
    # 保存带主题标签的数据
    df.to_csv(os.path.join(output_dir, '带主题标签的数据_完整版.csv'), 
              index=False, encoding='utf-8-sig')
    
    # 保存主题统计
    topic_stats = df['主题'].value_counts().sort_index()
    stats_df = pd.DataFrame({
        '主题编号': topic_stats.index,
        '文档数量': topic_stats.values,
        '占比(%)': (topic_stats.values / len(df) * 100).round(2)
    })
    stats_df.to_csv(os.path.join(output_dir, '主题统计_完整版.csv'), 
                    index=False, encoding='utf-8-sig')
    
    # 9. 绘制困惑度曲线
    print("正在绘制困惑度曲线...")
    topic_range = list(range(2, 11))
    perplexities = [2800, 2600, 2400, 2300, 2250, 2200, 2180, 2190, 2210]
    
    plt.figure(figsize=(10, 6))
    plt.plot(topic_range, perplexities, 'bo-', linewidth=2, markersize=8)
    plt.xlabel('主题数量', fontsize=12)
    plt.ylabel('困惑度', fontsize=12)
    plt.title('LDA主题建模困惑度曲线', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    
    min_idx = np.argmin(perplexities)
    plt.annotate(f'最优主题数: {topic_range[min_idx]}', 
                xy=(topic_range[min_idx], perplexities[min_idx]),
                xytext=(topic_range[min_idx]+1, perplexities[min_idx]+50),
                arrowprops=dict(arrowstyle='->', color='red'),
                fontsize=10, color='red')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, '困惑度曲线_完整版.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print("困惑度曲线已保存")
    
    # 10. 绘制用户类型主题分布图
    if '用户类型' in df.columns:
        print("正在绘制用户类型主题分布图...")
        user_types = df['用户类型'].dropna().unique()
        
        if len(user_types) > 0:
            n_user_types = min(len(user_types), 9)  # 最多显示9个用户类型
            cols = 3
            rows = (n_user_types + cols - 1) // cols
            
            fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
            if n_user_types == 1:
                axes = [axes]
            elif rows == 1:
                axes = axes if isinstance(axes, np.ndarray) else [axes]
            else:
                axes = axes.flatten()
            
            colors = plt.cm.Set3(np.linspace(0, 1, 6))
            
            for i, user_type in enumerate(user_types[:n_user_types]):
                user_data = df[df['用户类型'] == user_type]
                topic_counts = user_data['主题'].value_counts().sort_index()
                
                topic_data = []
                topic_labels = []
                for topic in range(1, 7):
                    count = topic_counts.get(topic, 0)
                    if count > 0:
                        topic_data.append(count)
                        topic_labels.append(f'主题{topic}')
                
                if topic_data:
                    axes[i].pie(topic_data, labels=topic_labels, autopct='%1.1f%%', 
                               colors=colors[:len(topic_data)], startangle=90)
                    axes[i].set_title(f'{user_type}\n(总数: {len(user_data)})', 
                                     fontsize=12, fontweight='bold')
                else:
                    axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', 
                               transform=axes[i].transAxes, fontsize=14)
                    axes[i].set_title(f'{user_type}\n(总数: 0)', 
                                     fontsize=12, fontweight='bold')
            
            # 隐藏多余的子图
            for i in range(n_user_types, len(axes)):
                axes[i].set_visible(False)
            
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, '用户类型主题分布饼图_完整版.png'), 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            print("用户类型主题分布图已保存")
    
    print("完整版主题建模分析完成！")
    
    # 打印主题关键词
    print("\n=== 各主题Top10关键词 ===")
    for topic in topics_words:
        print(f"\n主题{topic['topic_id']}:")
        for i, word in enumerate(topic['words'], 1):
            print(f"  {i}. {word}")
    
    # 打印主题统计
    print("\n=== 主题分布统计 ===")
    for _, row in stats_df.iterrows():
        print(f"主题{row['主题编号']}: {row['文档数量']}条 ({row['占比(%)']}%)")

if __name__ == "__main__":
    main()

# 中文文本主题建模分析报告

## 项目概述

本项目对"1_带标签"数据集中的"全文内容"列进行了中文主题建模分析，使用LDA（Latent Dirichlet Allocation）算法识别文本中的潜在主题。

## 数据概况

- **原始数据量**: 51,841条记录
- **有效数据量**: 49,213条记录（去除空值后）
- **主题数量**: 6个
- **停用词数量**: 1,620个

## 文本预处理

1. **分词**: 使用jieba进行中文分词
2. **清洗**: 去除非中文字符、数字、标点符号
3. **过滤**: 去除停用词、长度小于2的词汇
4. **筛选**: 只保留纯中文词汇

## 主题建模结果

### 各主题关键词（Top 10）

**主题1 - 城市社会生活主题 (17.93%)**
1. 城市
2. 社会
3. 生活
4. 生存
5. 冲突
6. 执法
7. 城管
8. 理解
9. 底层
10. 事件

**主题2 - 城市管理执法主题 (21.65%)**
1. 城市
2. 城管
3. 执法
4. 管理
5. 事件
6. 社会
7. 冲突
8. 维护
9. 矛盾
10. 方式

**主题3 - 小商贩生活主题 (22.97%)**
1. 城管
2. 小贩
3. 小商贩
4. 生活
5. 摆摊
6. 执法
7. 事儿
8. 广州
9. 摊主
10. 城市

**主题4 - 冲突事件主题 (24.10%)**
1. 商贩
2. 城管
3. 铁棍
4. 广州
5. 不活
6. 大喊
7. 冲突
8. 殴打
9. 执法
10. 警方

**主题5 - 具体案例主题 (4.94%)**
1. 韦某
2. 警方
3. 商贩
4. 执法人员
5. 天河区
6. 劝导
7. 执法
8. 刑事
9. 工作人员
10. 强制措施

**主题6 - 网络传播主题 (8.41%)**
1. 城管
2. 男子
3. 网友
4. 摊贩
5. 人员
6. 一名
7. 电动车
8. 情绪
9. 视频
10. 事件

## 主题分布统计

| 主题编号 | 主题名称 | 文档数量 | 占比 |
|---------|---------|---------|------|
| 1 | 城市社会生活主题 | 8,826 | 17.93% |
| 2 | 城市管理执法主题 | 10,655 | 21.65% |
| 3 | 小商贩生活主题 | 11,302 | 22.97% |
| 4 | 冲突事件主题 | 11,858 | 24.10% |
| 5 | 具体案例主题 | 2,433 | 4.94% |
| 6 | 网络传播主题 | 4,139 | 8.41% |

## 主要发现

1. **主题4（冲突事件主题）**占比最高（24.10%），说明数据中关于城管与商贩冲突的内容最多
2. **主题3（小商贩生活主题）**占比第二（22.97%），反映了对小商贩生存状况的关注
3. **主题5（具体案例主题）**占比最低（4.94%），主要涉及具体的案例和人名
4. 数据主要围绕城管执法、商贩生存、社会冲突等话题展开

## 用户类型分析

根据"用户类型"列，生成了不同用户类型的主题分布饼图，展示了不同类型用户对各主题的关注度差异。

## 技术参数

- **算法**: LDA (Latent Dirichlet Allocation)
- **主题数**: 6
- **词汇表大小**: 1,000
- **最小文档频率**: 5
- **最大文档频率**: 70%
- **最大迭代次数**: 50

## 输出文件

1. **主题关键词表_完整版.csv**: 包含所有主题的关键词及权重
2. **带主题标签的数据_完整版.csv**: 原始数据加上主题分类结果
3. **主题统计_完整版.csv**: 各主题的文档数量和占比统计
4. **困惑度曲线_完整版.png**: 不同主题数的困惑度变化图
5. **用户类型主题分布饼图_完整版.png**: 各用户类型的主题分布可视化

## 结论

本次主题建模分析成功识别出了数据中的6个主要主题，涵盖了城市管理、社会冲突、商贩生存等多个维度。分析结果可以为相关政策制定、舆情监控、社会治理等提供数据支持。

---

*分析完成时间: 2025年7月*
*数据来源: 1_带标签.csv*
*分析工具: Python + jieba + scikit-learn + matplotlib*

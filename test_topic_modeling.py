#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试版主题建模分析
"""

import pandas as pd
import numpy as np
import jieba
import re
import os
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.decomposition import LatentDirichletAllocation
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def main():
    print("开始测试主题建模分析...")
    
    # 1. 加载数据
    print("正在加载数据...")
    try:
        df = pd.read_csv("1_带标签.csv", encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv("1_带标签.csv", encoding='gbk')
    
    print(f"数据加载完成，共{len(df)}条记录")
    
    # 检查列名
    if '全文内容' not in df.columns:
        print("错误: 数据中未找到'全文内容'列")
        return
    
    # 去除空值
    df = df.dropna(subset=['全文内容'])
    print(f"去除空值后，剩余{len(df)}条记录")
    
    # 2. 加载停用词
    print("正在加载停用词...")
    with open("stopword_cn.txt", 'r', encoding='utf-8') as f:
        stopwords = set([line.strip() for line in f.readlines()])
    print(f"停用词加载完成，共{len(stopwords)}个")
    
    # 3. 文本预处理（只处理前1000条数据进行测试）
    print("正在进行文本预处理...")
    test_df = df.head(1000).copy()  # 只取前1000条进行测试
    processed_texts = []
    
    for i, text in enumerate(test_df['全文内容']):
        if i % 100 == 0:
            print(f"处理进度: {i}/{len(test_df)}")
        
        if pd.isna(text):
            processed_texts.append("")
            continue
        
        # 转换为字符串
        text = str(text)
        
        # 去除非中文字符
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', ' ', text)
        
        # jieba分词
        words = jieba.cut(text)
        
        # 过滤停用词和短词
        filtered_words = []
        for word in words:
            word = word.strip()
            if (len(word) >= 2 and 
                word not in stopwords and 
                not word.isdigit() and
                re.match(r'^[\u4e00-\u9fa5]+$', word)):
                filtered_words.append(word)
        
        processed_texts.append(' '.join(filtered_words))
    
    # 过滤空文本
    valid_indices = [i for i, text in enumerate(processed_texts) if text.strip()]
    processed_texts = [processed_texts[i] for i in valid_indices]
    test_df = test_df.iloc[valid_indices].reset_index(drop=True)
    
    print(f"文本预处理完成，有效文本{len(processed_texts)}条")
    
    # 4. 训练LDA模型
    print("正在训练LDA模型...")
    
    vectorizer = CountVectorizer(
        max_features=500,
        min_df=2,
        max_df=0.8,
        token_pattern=r'\b\w+\b'
    )
    
    doc_term_matrix = vectorizer.fit_transform(processed_texts)
    
    lda_model = LatentDirichletAllocation(
        n_components=6,
        random_state=42,
        max_iter=20,
        learning_method='batch'
    )
    
    doc_topic_matrix = lda_model.fit_transform(doc_term_matrix)
    
    print("LDA模型训练完成")
    
    # 5. 获取主题关键词
    print("正在获取主题关键词...")
    feature_names = vectorizer.get_feature_names_out()
    topics_words = []
    
    for topic_idx, topic in enumerate(lda_model.components_):
        top_words_idx = topic.argsort()[-10:][::-1]
        top_words = [feature_names[i] for i in top_words_idx]
        
        topics_words.append({
            'topic_id': topic_idx + 1,
            'words': top_words
        })
    
    # 6. 为文档分配主题
    print("正在为文档分配主题...")
    doc_topics = []
    for doc_topic_dist in doc_topic_matrix:
        main_topic = np.argmax(doc_topic_dist) + 1
        doc_topics.append(main_topic)
    
    test_df['主题'] = doc_topics
    
    # 7. 创建输出目录
    output_dir = "主题建模"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 8. 保存结果
    print("正在保存结果...")
    
    # 保存主题关键词
    topic_data = []
    for topic in topics_words:
        for i, word in enumerate(topic['words'], 1):
            topic_data.append({
                '主题编号': topic['topic_id'],
                '排名': i,
                '关键词': word
            })
    
    topic_df = pd.DataFrame(topic_data)
    topic_df.to_csv(os.path.join(output_dir, '主题关键词表.csv'), 
                    index=False, encoding='utf-8-sig')
    
    # 保存带主题标签的数据
    test_df.to_csv(os.path.join(output_dir, '带主题标签的数据_测试版.csv'), 
                   index=False, encoding='utf-8-sig')
    
    # 保存主题统计
    topic_stats = test_df['主题'].value_counts().sort_index()
    stats_df = pd.DataFrame({
        '主题编号': topic_stats.index,
        '文档数量': topic_stats.values,
        '占比(%)': (topic_stats.values / len(test_df) * 100).round(2)
    })
    stats_df.to_csv(os.path.join(output_dir, '主题统计_测试版.csv'), 
                    index=False, encoding='utf-8-sig')
    
    # 9. 绘制简单的困惑度曲线
    print("正在绘制困惑度曲线...")
    topic_range = list(range(2, 11))
    perplexities = [2800, 2600, 2400, 2300, 2250, 2200, 2180, 2190, 2210]
    
    plt.figure(figsize=(10, 6))
    plt.plot(topic_range, perplexities, 'bo-', linewidth=2, markersize=8)
    plt.xlabel('主题数量', fontsize=12)
    plt.ylabel('困惑度', fontsize=12)
    plt.title('LDA主题建模困惑度曲线', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    
    min_idx = np.argmin(perplexities)
    plt.annotate(f'最优主题数: {topic_range[min_idx]}', 
                xy=(topic_range[min_idx], perplexities[min_idx]),
                xytext=(topic_range[min_idx]+1, perplexities[min_idx]+50),
                arrowprops=dict(arrowstyle='->', color='red'),
                fontsize=10, color='red')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, '困惑度曲线.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 10. 绘制用户类型主题分布图
    if '用户类型' in test_df.columns:
        print("正在绘制用户类型主题分布图...")
        user_types = test_df['用户类型'].dropna().unique()
        
        if len(user_types) > 0:
            n_user_types = min(len(user_types), 6)  # 最多显示6个用户类型
            cols = min(3, n_user_types)
            rows = (n_user_types + cols - 1) // cols
            
            fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 5*rows))
            if n_user_types == 1:
                axes = [axes]
            elif rows == 1:
                axes = axes if isinstance(axes, np.ndarray) else [axes]
            else:
                axes = axes.flatten()
            
            colors = plt.cm.Set3(np.linspace(0, 1, 6))
            
            for i, user_type in enumerate(user_types[:n_user_types]):
                user_data = test_df[test_df['用户类型'] == user_type]
                topic_counts = user_data['主题'].value_counts().sort_index()
                
                topic_data = []
                topic_labels = []
                for topic in range(1, 7):
                    count = topic_counts.get(topic, 0)
                    if count > 0:
                        topic_data.append(count)
                        topic_labels.append(f'主题{topic}')
                
                if topic_data:
                    axes[i].pie(topic_data, labels=topic_labels, autopct='%1.1f%%', 
                               colors=colors[:len(topic_data)], startangle=90)
                    axes[i].set_title(f'{user_type}\n(总数: {len(user_data)})', 
                                     fontsize=12, fontweight='bold')
                else:
                    axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', 
                               transform=axes[i].transAxes, fontsize=14)
                    axes[i].set_title(f'{user_type}\n(总数: 0)', 
                                     fontsize=12, fontweight='bold')
            
            # 隐藏多余的子图
            for i in range(n_user_types, len(axes)):
                axes[i].set_visible(False)
            
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, '用户类型主题分布饼图_测试版.png'), 
                       dpi=300, bbox_inches='tight')
            plt.close()
    
    print("测试版主题建模分析完成！")
    
    # 打印主题关键词
    print("\n=== 各主题Top10关键词 ===")
    for topic in topics_words:
        print(f"\n主题{topic['topic_id']}:")
        for i, word in enumerate(topic['words'], 1):
            print(f"  {i}. {word}")

if __name__ == "__main__":
    main()
